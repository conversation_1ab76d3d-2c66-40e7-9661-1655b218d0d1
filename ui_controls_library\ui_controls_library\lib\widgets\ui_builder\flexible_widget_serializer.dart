import 'package:flutter/material.dart';
import 'dart:convert';
// import 'package:pie_chart/pie_chart.dart';
// import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:graphic/graphic.dart' as graphic;
import '../date_range_widget.dart';




/// Runtime Property Discovery Serializer
/// Can serialize any Flutter widget dynamically without custom toJson/fromJson methods
class FlexibleWidgetSerializer {
  
  /// Custom serializers for specific widget types
  static final Map<Type, Function> _customSerializers = {
    
    // Container serialization
    Container: (Container w) => {
      'type': 'Container',
      'width': w.constraints?.maxWidth != double.infinity ? w.constraints?.maxWidth : 
               (w.constraints?.minWidth != 0.0 ? w.constraints?.minWidth : null),
      'height': w.constraints?.maxHeight != double.infinity ? w.constraints?.maxHeight : 
                (w.constraints?.minHeight != 0.0 ? w.constraints?.minHeight : null),
      // Only serialize decoration if it exists, otherwise serialize color
      'decoration': w.decoration != null ? _serializeBoxDecoration(w.decoration as BoxDecoration?) : null,
      'color': w.decoration == null && w.color != null ? w.color!.value : null,
      'padding': w.padding != null ? _serializeEdgeInsets(w.padding!) : null,
      'margin': w.margin != null ? _serializeEdgeInsets(w.margin!) : null,
      'child': w.child != null ? serialize(w.child!) : null,
    },
    
    // Text serialization
    Text: (Text w) => {
      'type': 'Text',
      'data': w.data ?? '',
      'style': w.style != null ? {
        'fontSize': w.style!.fontSize,
        'color': w.style!.color?.value,
        'fontWeight': w.style!.fontWeight?.index,
        'fontFamily': w.style!.fontFamily,
      } : null,
      'textAlign': w.textAlign?.index,
      'maxLines': w.maxLines,
      'overflow': w.overflow?.index,
    },
    
    // Column serialization
    Column: (Column w) => {
      'type': 'Column',
      'mainAxisAlignment': w.mainAxisAlignment.index,
      'crossAxisAlignment': w.crossAxisAlignment.index,
      'mainAxisSize': w.mainAxisSize.index,
      'children': w.children.map((child) => serialize(child)).toList(),
    },
    
    // Row serialization
    Row: (Row w) => {
      'type': 'Row',
      'mainAxisAlignment': w.mainAxisAlignment.index,
      'crossAxisAlignment': w.crossAxisAlignment.index,
      'mainAxisSize': w.mainAxisSize.index,
      'children': w.children.map((child) => serialize(child)).toList(),
    },
    
   

    // IconButton serialization
    IconButton: (IconButton w) => {
      'type': 'IconButton',
      'iconSize': w.iconSize,
      'color': w.color?.value,
      'icon': w.icon is Icon ? {
        'type': 'Icon',
        'iconData': (w.icon as Icon).icon?.codePoint,
        'size': (w.icon as Icon).size,
        'color': (w.icon as Icon).color?.value,
      } : null,
      'tooltip': w.tooltip,
    },

    // Icon serialization
    Icon: (Icon w) => {
      'type': 'Icon',
      'iconData': w.icon?.codePoint,
      'size': w.size,
      'color': w.color?.value,
    },

    // Center serialization
    Center: (Center w) => {
      'type': 'Center',
      'child': w.child != null ? serialize(w.child!) : null,
    },

    // Expanded serialization
    Expanded: (Expanded w) => {
      'type': 'Expanded',
      'flex': w.flex,
      'child': serialize(w.child),
    },
    
    // SizedBox serialization
    SizedBox: (SizedBox w) => {
      'type': 'SizedBox',
      'width': w.width,
      'height': w.height,
      'child': w.child != null ? serialize(w.child!) : null,
    },
    
    // Padding serialization
    Padding: (Padding w) => {
      'type': 'Padding',
      'padding': _serializeEdgeInsets(w.padding),
      'child': w.child != null ? serialize(w.child!) : null,
    },

    // Card serialization
    Card: (Card w) => {
      'type': 'Card',
      'elevation': w.elevation,
      'shape': w.shape != null ? _serializeShapeBorder(w.shape!) : null,
      'child': w.child != null ? serialize(w.child!) : null,
    },

    // LayoutBuilder serialization
    LayoutBuilder: (LayoutBuilder w) => {
      'type': 'LayoutBuilder',
      // Note: We can't serialize the builder function, so we'll create a placeholder
      'placeholder': 'LayoutBuilder - requires runtime context',
    },

    // AspectRatio serialization
    AspectRatio: (AspectRatio w) => {
      'type': 'AspectRatio',
      'aspectRatio': w.aspectRatio,
      'child': w.child != null ? serialize(w.child!) : null,
    },

    // Note: Syncfusion chart serializers removed due to dependency issues

    // Graphic Chart serialization (for scatter and bar charts using graphic package)
    graphic.Chart: (graphic.Chart w) => {
      'type': 'GraphicChart',
      'chartType': _determineGraphicChartType(w), // Determine chart type from marks
      'data': w.data,
      'variables': _serializeGraphicVariables(w.variables),
      'marks': w.marks.map((mark) => {
        'type': mark.runtimeType.toString(),
        'size': (mark is graphic.PointMark && mark.size != null) ?
          (mark.size as graphic.SizeEncode?)?.value :
          (mark is graphic.IntervalMark && mark.size != null) ?
          (mark.size as graphic.SizeEncode?)?.value : null,
        'colors': mark is graphic.PointMark && mark.color != null ?
          (mark.color as graphic.ColorEncode?)?.values?.map((color) =>
            color is Color ? color.value : color).toList() : null,
      }).toList(),
      'coord': w.coord?.runtimeType.toString(),
      'coordConfig': w.coord != null ? _serializePolarCoordConfig(w.coord!) : null,
      'axes': w.axes?.map((axis) => axis.runtimeType.toString()).toList(),
    },

    // Stack serialization (needed for donut charts)
    Stack: (Stack w) => {
      'type': 'Stack',
      'alignment': w.alignment.toString(),
      'children': w.children.map((child) => serialize(child)).toList(),
      'isDonutChart': _isDonutChartStack(w), // Custom detection for donut charts
    },

    // DateRangeWidget serialization
    DateRangeWidget: (DateRangeWidget w) => {
      'type': 'DateRangeWidget',
      'initialStartDate': w.initialStartDate?.millisecondsSinceEpoch,
      'initialEndDate': w.initialEndDate?.millisecondsSinceEpoch,
      'allowDateSelection': w.allowDateSelection,
      'format': w.format.toString().split('.').last,
      'formatPattern': w.formatPattern,
      'showWeekday': w.showWeekday,
      'showYear': w.showYear,
      'showMonth': w.showMonth,
      'showDay': w.showDay,
      'locale': w.locale,
      'textColor': w.textColor.value,
      'backgroundColor': w.backgroundColor.value,
      'fontSize': w.fontSize,
      'fontWeight': w.fontWeight.index,
      'fontFamily': w.fontFamily,
      'textAlign': w.textAlign.index,
      'hasBorder': w.hasBorder,
      'borderRadius': w.borderRadius,
      'borderColor': w.borderColor.value,
      'borderWidth': w.borderWidth,
      'hasShadow': w.hasShadow,
      'elevation': w.elevation,
      'isCompact': w.isCompact,
      'label': w.label,
      'prefix': w.prefix,
      'suffix': w.suffix,
      'showCalendarIcon': w.showCalendarIcon,
      'calendarIcon': w.calendarIcon.codePoint,
      'calendarIconColor': w.calendarIconColor?.value,
      'isDarkTheme': w.isDarkTheme,
      'enabled': w.enabled,
      'readOnly': w.readOnly,
      'minDate': w.minDate?.millisecondsSinceEpoch,
      'maxDate': w.maxDate?.millisecondsSinceEpoch,
      'hoverColor': w.hoverColor?.value,
      'focusColor': w.focusColor?.value,
      'autofocus': w.autofocus,
      'useJsonCallbacks': w.useJsonCallbacks,
      'jsonConfig': w.jsonConfig,
    },
  };
  
  /// Helper method to determine graphic chart type from marks
  static String _determineGraphicChartType(graphic.Chart chart) {
    // Check coordinate system first
    if (chart.coord is graphic.PolarCoord) {
      if (chart.marks.any((mark) => mark is graphic.IntervalMark)) {
        final polarCoord = chart.coord as graphic.PolarCoord;
        // Donut charts have transposed: true and dimCount: 1
        if (polarCoord.transposed == true && polarCoord.dimCount == 1) {
          return 'donut';
        }
        // Regular pie charts have default polar coordinates
        return 'pie';
      }
    }

    if (chart.marks.any((mark) => mark is graphic.IntervalMark)) {
      return 'bar';
    } else if (chart.marks.any((mark) => mark is graphic.PointMark)) {
      // Check if it's a bubble chart by looking for size encoding
      final pointMark = chart.marks.firstWhere((mark) => mark is graphic.PointMark) as graphic.PointMark;
      if (pointMark.size != null && pointMark.size is graphic.SizeEncode) {
        final sizeEncode = pointMark.size as graphic.SizeEncode;
        if (sizeEncode.variable != null) {
          return 'bubble';
        }
      }
      return 'scatter';
    }
    return 'scatter'; // Default fallback
  }

  /// Helper method to serialize graphic chart variables
  static Map<String, dynamic> _serializeGraphicVariables(Map<String, graphic.Variable> variables) {
    final result = <String, dynamic>{};

    for (final entry in variables.entries) {
      final key = entry.key;
      final variable = entry.value;

      if (variable.scale is graphic.LinearScale) {
        final scale = variable.scale as graphic.LinearScale;
        result[key] = {
          'min': scale.min,
          'max': scale.max,
          'tickCount': scale.tickCount,
        };
      }
    }

    return result;
  }

  /// Helper method to serialize polar coordinate configuration
  static Map<String, dynamic>? _serializePolarCoordConfig(graphic.Coord coord) {
    if (coord is graphic.PolarCoord) {
      return {
        'transposed': coord.transposed,
        'dimCount': coord.dimCount,
      };
    }
    return null;
  }

  /// Helper method to detect if a Stack contains a donut chart
  static bool _isDonutChartStack(Stack stack) {
    // Check if the stack has a graphic.Chart and a center text overlay
    bool hasChart = false;
    bool hasCenterText = false;

    for (final child in stack.children) {
      if (child is graphic.Chart) {
        hasChart = true;
        // Check if it's a polar coordinate chart (pie/donut)
        if (child.coord is graphic.PolarCoord) {
          final polarCoord = child.coord as graphic.PolarCoord;
          // Donut charts typically have transposed: true and dimCount: 1
          if (polarCoord.transposed == true && polarCoord.dimCount == 1) {
            return true;
          }
        }
      } else if (child is Center) {
        hasCenterText = true;
      }
    }

    return hasChart && hasCenterText;
  }

  /// Helper method to parse alignment from string
  static AlignmentGeometry _parseAlignment(String? alignmentStr) {
    if (alignmentStr == null) return Alignment.center;

    switch (alignmentStr) {
      case 'Alignment.topLeft':
        return Alignment.topLeft;
      case 'Alignment.topCenter':
        return Alignment.topCenter;
      case 'Alignment.topRight':
        return Alignment.topRight;
      case 'Alignment.centerLeft':
        return Alignment.centerLeft;
      case 'Alignment.center':
        return Alignment.center;
      case 'Alignment.centerRight':
        return Alignment.centerRight;
      case 'Alignment.bottomLeft':
        return Alignment.bottomLeft;
      case 'Alignment.bottomCenter':
        return Alignment.bottomCenter;
      case 'Alignment.bottomRight':
        return Alignment.bottomRight;
      default:
        return Alignment.center;
    }
  }

  /// Main serialization method - discovers widget type at runtime
  static Map<String, dynamic> serialize(Widget widget) {
    Function? serializer = _customSerializers[widget.runtimeType];

    // Special handling for graphic.Chart which has generic type parameters
    if (serializer == null && widget is graphic.Chart) {
      serializer = _customSerializers[graphic.Chart];
    }

    Map<String, dynamic> base = {
      'runtimeType': widget.runtimeType.toString(),
      'key': widget.key?.toString(),
      'hashCode': widget.hashCode,
    };

    if (serializer != null) {
      try {
        final serialized = serializer(widget);
        base.addAll(serialized);
        
        // Debug print for graphic charts
        if (widget is graphic.Chart) {
          print("=== GRAPHIC CHART SERIALIZED ===");
          print("Type: ${widget.runtimeType}");
          print("Data: ${widget.data}");
          print("Serialized: $serialized");
        }
      } catch (e) {
        print("=== SERIALIZATION ERROR ===");
        print("Widget: ${widget.runtimeType}");
        print("Error: $e");
        base['serializationError'] = e.toString();
        base.addAll(_extractBasicProperties(widget));
      }
    } else {
      print("=== UNSUPPORTED WIDGET ===");
      print("Widget: ${widget.runtimeType}");
      base['unsupported'] = true;
      base.addAll(_extractBasicProperties(widget));
    }

    return base;
  }
  
  /// Fallback property extraction for unsupported widgets
  static Map<String, dynamic> _extractBasicProperties(Widget widget) {
    return {
      'runtimeType': widget.runtimeType.toString(),
      'hashCode': widget.hashCode,
      'toString': widget.toString(),
    };
  }
  
  /// Helper: Serialize EdgeInsets
  static Map<String, dynamic> _serializeEdgeInsets(EdgeInsetsGeometry insets) {
    final resolved = insets.resolve(TextDirection.ltr);
    return {
      'left': resolved.left,
    'top': resolved.top,
    'right': resolved.right,
    'bottom': resolved.bottom,
    };
  }
  
  /// Helper: Serialize BoxDecoration
  static Map<String, dynamic>? _serializeBoxDecoration(BoxDecoration? decoration) {
    if (decoration == null) return null;
    
    return {
      'color': decoration.color?.value,
      'borderRadius': decoration.borderRadius?.toString(),
      'border': decoration.border?.toString(),
      'boxShadow': decoration.boxShadow?.map((shadow) => {
        'color': shadow.color.value,
        'offset': {'dx': shadow.offset.dx, 'dy': shadow.offset.dy},
        'blurRadius': shadow.blurRadius,
        'spreadRadius': shadow.spreadRadius,
      }).toList(),
    };
  }

  /// Helper: Serialize ShapeBorder
  static Map<String, dynamic>? _serializeShapeBorder(ShapeBorder shape) {
    if (shape is RoundedRectangleBorder) {
      return {
        'type': 'RoundedRectangleBorder',
        'borderRadius': shape.borderRadius.toString(),
        'side': shape.side != BorderSide.none ? {
          'color': shape.side.color.value,
          'width': shape.side.width,
        } : null,
      };
    }
    return {
      'type': shape.runtimeType.toString(),
      'data': shape.toString(),
    };
  }
  
  /// Convert to pretty JSON string
  static String toJsonString(Widget widget, {bool prettyPrint = false}) {
    final json = serialize(widget);
    if (prettyPrint) {
      const JsonEncoder encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(json);
    }
    return jsonEncode(json);
  }
  
  /// Deserialize JSON back to Widget (comprehensive support)
  static Widget? deserialize(Map<String, dynamic> json) {
    final type = json['type'];

    switch (type) {
     

      case 'Container':
        // Handle the Flutter constraint: cannot have both color and decoration
        BoxDecoration? decoration;
        Color? color;

        if (json['decoration'] != null) {
          decoration = _parseBoxDecoration(json['decoration']);
        } else if (json['color'] != null) {
          color = Color(json['color']);
        }

        return Container(
          width: json['width']?.toDouble(),
          height: json['height']?.toDouble(),
          color: color,
          decoration: decoration,
          padding: json['padding'] != null ? _parseEdgeInsets(json['padding']) : null,
          margin: json['margin'] != null ? _parseEdgeInsets(json['margin']) : null,
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      case 'Column':
        return Column(
          mainAxisAlignment: _parseMainAxisAlignment(json['mainAxisAlignment']),
          crossAxisAlignment: _parseCrossAxisAlignment(json['crossAxisAlignment']),
          mainAxisSize: _parseMainAxisSize(json['mainAxisSize']),
          children: json['children'] != null
              ? (json['children'] as List).map((child) => deserialize(child) ?? const SizedBox()).toList()
              : [],
        );

      case 'Row':
        return Row(
          mainAxisAlignment: _parseMainAxisAlignment(json['mainAxisAlignment']),
          crossAxisAlignment: _parseCrossAxisAlignment(json['crossAxisAlignment']),
          mainAxisSize: _parseMainAxisSize(json['mainAxisSize']),
          children: json['children'] != null
              ? (json['children'] as List).map((child) => deserialize(child) ?? const SizedBox()).toList()
              : [],
        );

      case 'Expanded':
        return Expanded(
          flex: json['flex'] ?? 1,
          child: json['child'] != null ? deserialize(json['child']) ?? const SizedBox() : const SizedBox(),
        );

      case 'Text':
        final textData = json['data'];
        return Text(
          textData?.toString() ?? '', // Use empty string if data is null
          textAlign: _parseTextAlign(json['textAlign']),
          maxLines: json['maxLines'],
          overflow: _parseTextOverflow(json['overflow']),
          style: json['style'] != null ? _parseTextStyle(json['style']) : null,
        );

      case 'IconButton':
        return IconButton(
          onPressed: () {}, // Placeholder function
          icon: _parseIcon(json['icon']),
          iconSize: json['iconSize']?.toDouble(),
          color: json['color'] != null ? Color(json['color']) : null,
          tooltip: json['tooltip'],
        );

      case 'Icon':
        return _parseIcon(json);

      case 'Center':
        return Center(
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      case 'Padding':
        return Padding(
          padding: _parseEdgeInsets(json['padding']) ?? EdgeInsets.zero,
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      case 'SizedBox':
        return SizedBox(
          width: json['width']?.toDouble(),
          height: json['height']?.toDouble(),
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      case 'Card':
        return Card(
          elevation: json['elevation']?.toDouble() ?? 1.0,
          shape: json['shape'] != null ? _parseShapeBorder(json['shape']) : null,
          child: json['child'] != null ? deserialize(json['child']) : null,

        );

      case 'Stack':
        final children = (json['children'] as List?)
            ?.map((child) => deserialize(child))
            .where((child) => child != null)
            .cast<Widget>()
            .toList() ?? [];

        // Check if this is a donut chart stack
        if (json['isDonutChart'] == true) {
          // For donut charts, wrap in a Container with proper sizing and ensure circular center
          return Container(
            width: 280.0, // Default size
            height: 280.0,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Chart
                if (children.isNotEmpty) children.first,
                // Center text overlay with fixed circular background (constant size)
                Container(
                  width: 100.0, // Fixed size for all chart sizes
                  height: 100.0, // Fixed size for all chart sizes
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "Total Value",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 8.0, // Fixed font size
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          "\$9,999.99",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 12.0, // Fixed font size
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return Stack(
          alignment: _parseAlignment(json['alignment']),
          children: children,
        );

      // Note: Syncfusion chart deserialization removed due to dependency issues
      case 'SfCircularChart':
      case 'SfCartesianChart':
        return Container(
          height: 200,
          child: Center(
            child: Text('Chart deserialization not available'),
          ),
        );

      case 'AspectRatio':
        return AspectRatio(
          aspectRatio: json['aspectRatio']?.toDouble() ?? 1.0,
          child: json['child'] != null ? deserialize(json['child']) : null,
        );

      case 'DateRangeWidget':
        return DateRangeWidget(
          initialStartDate: json['initialStartDate'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['initialStartDate'])
            : null,
          initialEndDate: json['initialEndDate'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['initialEndDate'])
            : null,
          allowDateSelection: json['allowDateSelection'] ?? true,
          format: _parseDateRangeFormat(json['format']),
          formatPattern: json['formatPattern'],
          showWeekday: json['showWeekday'] ?? false,
          showYear: json['showYear'] ?? true,
          showMonth: json['showMonth'] ?? true,
          showDay: json['showDay'] ?? true,
          locale: json['locale'] ?? 'en_US',
          textColor: json['textColor'] != null ? Color(json['textColor']) : Colors.black,
          backgroundColor: json['backgroundColor'] != null ? Color(json['backgroundColor']) : Colors.white,
          fontSize: json['fontSize']?.toDouble() ?? 16.0,
          fontWeight: _parseFontWeight(json['fontWeight']) ?? FontWeight.normal,
          fontFamily: json['fontFamily'],
          textAlign: _parseTextAlign(json['textAlign']) ?? TextAlign.start,
          hasBorder: json['hasBorder'] ?? true,
          borderRadius: json['borderRadius']?.toDouble() ?? 4.0,
          borderColor: json['borderColor'] != null ? Color(json['borderColor']) : const Color(0xFFCCCCCC),
          borderWidth: json['borderWidth']?.toDouble() ?? 1.0,
          hasShadow: json['hasShadow'] ?? false,
          elevation: json['elevation']?.toDouble() ?? 2.0,
          isCompact: json['isCompact'] ?? false,
          label: json['label'],
          prefix: json['prefix'],
          suffix: json['suffix'],
          showCalendarIcon: json['showCalendarIcon'] ?? true,
          calendarIcon: json['calendarIcon'] != null
            ? IconData(json['calendarIcon'], fontFamily: 'MaterialIcons')
            : Icons.calendar_today,
          calendarIconColor: json['calendarIconColor'] != null ? Color(json['calendarIconColor']) : null,
          isDarkTheme: json['isDarkTheme'] ?? false,
          enabled: json['enabled'] ?? true,
          readOnly: json['readOnly'] ?? false,
          minDate: json['minDate'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['minDate'])
            : null,
          maxDate: json['maxDate'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['maxDate'])
            : null,
          hoverColor: json['hoverColor'] != null ? Color(json['hoverColor']) : null,
          focusColor: json['focusColor'] != null ? Color(json['focusColor']) : null,
          autofocus: json['autofocus'] ?? false,
          useJsonCallbacks: json['useJsonCallbacks'] ?? false,
          jsonConfig: json['jsonConfig'],
        );

      case 'GraphicChart':
        print("=== DESERIALIZING GRAPHIC CHART ===");
        print("JSON data: $json");
        
        // Deserialize Graphic Chart (pie, donut, scatter, bar, or bubble chart)
        final chartType = json['chartType'] as String? ?? 'scatter';
        final data = json['data'] as List<dynamic>? ?? [];
        final variables = json['variables'] as Map<String, dynamic>? ?? {};
        final marks = json['marks'] as List<dynamic>? ?? [];

        print("Chart type: $chartType");
        print("Data points: ${data.length}");
        print("Variables: $variables");
        print("Marks: $marks");

        // Extract colors from marks if available
        List<Color> colors = [
          const Color(0xFF0D47A1), // Dark Blue for Food
          const Color(0xFF1565C0), // Medium Dark Blue for Rent
          const Color(0xFF1976D2), // Blue for Transport
          const Color(0xFF1E88E5), // Medium Blue for Savings
          const Color(0xFF2196F3), // Light Blue for Others
          const Color(0xFF42A5F5), // Lighter Blue for Utilities
          const Color(0xFF64B5F6), // Very Light Blue for Insurance
          const Color(0xFF90CAF9), // Lightest Blue for Entertainment
        ];

        if (marks.isNotEmpty) {
          final firstMark = marks.first as Map<String, dynamic>;
          final markColors = firstMark['colors'] as List<dynamic>?;
          if (markColors != null) {
            colors = markColors.map((c) => Color(c as int)).toList();
            print("Extracted colors: ${colors.length} colors");
          }
        }

        // Extract size from marks if available
        double pointSize = 8.0;
        if (marks.isNotEmpty) {
          final firstMark = marks.first as Map<String, dynamic>;
          pointSize = (firstMark['size'] as num?)?.toDouble() ?? 8.0;
          print("Point/bar size: $pointSize");
        }

        print("Creating graphic chart with ${data.length} data points");

        try {
          Widget chart;
          
          // For pie and donut charts, use appropriate sizing
          if (chartType == 'pie' || chartType == 'donut') {
            // Extract chart size from the serialized data if available
            double chartSize = 280.0; // Default size
            
            // Try to get the actual chart size from the container data
            final containerData = json['children']?.firstWhere(
              (child) => child['type'] == 'Container' && child['width'] != null,
              orElse: () => null,
            );
            
            if (containerData != null && containerData['width'] != null) {
              chartSize = (containerData['width'] as num).toDouble();
            }
            
            chart = Container(
              width: chartSize,
              height: chartSize,
              child: _createGraphicChart(chartType, data, variables, colors, pointSize),
            );
          } else {
            // For other chart types, use height-based container
            chart = Container(
              height: 300,
              child: _createGraphicChart(chartType, data, variables, colors, pointSize),
            );
          }

          print("Successfully created $chartType chart");
          return chart;
        } catch (e) {
          print("Error creating graphic chart: $e");
          return Container(
            height: 300,
            child: Center(child: Text('Error loading $chartType chart: $e')),
          );
        }

      default:
        print("Unknown widget type: ${json['type']}");
        return Container(child: Text('Unknown widget: ${json['type']}'));
    }
  }

  /// Helper method to create graphic charts based on type
  static graphic.Chart _createGraphicChart(String chartType, List<dynamic> data,
      Map<String, dynamic> variables, List<Color> colors, double pointSize) {

    if (chartType == 'bar') {
      // Create bar chart
      return graphic.Chart(
        data: data,
        variables: {
          'category': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['category'] as String,
          ),
          'value': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['value'] as num,
            scale: graphic.LinearScale(
              min: (variables['value']?['min'] as num?)?.toDouble() ?? 0,
              max: (variables['value']?['max'] as num?)?.toDouble() ?? 120,
              tickCount: (variables['value']?['tickCount'] as num?)?.toInt() ?? 7,
            ),
          ),
          'index': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['index'] as num,
          ),
        },
        marks: [
          graphic.IntervalMark(
            position: graphic.Varset('category') * graphic.Varset('value'),
            color: graphic.ColorEncode(
              variable: 'index',
              values: colors,
            ),
            size: graphic.SizeEncode(value: pointSize), // Apply the serialized bar width
          ),
        ],
        axes: [
          graphic.Defaults.horizontalAxis,
          graphic.Defaults.verticalAxis,
        ],
        coord: graphic.RectCoord(),
      );
    } else if (chartType == 'pie') {
      // Create pie chart with proper configuration for full pie
      return graphic.Chart(
        data: data,
        variables: {
          'category': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['category'] as String,
          ),
          'value': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['value'] as num,
            scale: graphic.LinearScale(min: 0),
          ),
          'index': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['index'] as num,
          ),
        },
        marks: [
          graphic.IntervalMark(
            position: graphic.Varset('value'),
            color: graphic.ColorEncode(variable: 'index', values: colors),
            size: graphic.SizeEncode(value: 1.0), // Full pie chart - no inner hole
          ),
        ],
        coord: graphic.PolarCoord(
          transposed: true,
          dimCount: 1,
        ),
      );
    } else if (chartType == 'donut') {
      // Create donut chart
      return graphic.Chart(
        data: data,
        variables: {
          'category': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['category'] as String,
          ),
          'value': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['value'] as num,
            scale: graphic.LinearScale(min: 0),
          ),
          'index': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['index'] as num,
          ),
        },
        marks: [
          graphic.IntervalMark(
            position: graphic.Varset('value'),
            color: graphic.ColorEncode(variable: 'index', values: colors),
            size: graphic.SizeEncode(value: 0.6), // Creates the donut hole
          ),
        ],
        coord: graphic.PolarCoord(
          transposed: true,
          dimCount: 1,
        ),
      );
    } else if (chartType == 'bubble') {
      // Create bubble chart
      return graphic.Chart(
        data: data,
        variables: {
          'x': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['x'] as num,
            scale: graphic.LinearScale(min: 0, max: 100, tickCount: 6),
          ),
          'y': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['y'] as num,
            scale: graphic.LinearScale(min: 0, max: 50, tickCount: 6),
          ),
          'size': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['size'] as num,
          ),
          'index': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['index'] as num,
          ),
        },
        marks: [
          graphic.PointMark(
            position: graphic.Varset('x') * graphic.Varset('y'),
            size: graphic.SizeEncode(
              variable: 'size',
              values: [5, 10, 15, 20, 25, 30, 35, 40],
            ),
            color: graphic.ColorEncode(variable: 'index', values: colors),
            shape: graphic.ShapeEncode(value: graphic.CircleShape()),
          ),
        ],
        axes: [graphic.Defaults.horizontalAxis, graphic.Defaults.verticalAxis],
        coord: graphic.RectCoord(),
      );
    } else {
      // Create scatter chart (default)
      return graphic.Chart(
        data: data,
        variables: {
          'x': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['x'] as num,
            scale: graphic.LinearScale(
              min: (variables['x']?['min'] as num?)?.toDouble() ?? 0,
              max: (variables['x']?['max'] as num?)?.toDouble() ?? 35,
              tickCount: (variables['x']?['tickCount'] as num?)?.toInt() ?? 8,
            ),
          ),
          'y': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['y'] as num,
            scale: graphic.LinearScale(
              min: (variables['y']?['min'] as num?)?.toDouble() ?? 2000,
              max: (variables['y']?['max'] as num?)?.toDouble() ?? 8000,
              tickCount: (variables['y']?['tickCount'] as num?)?.toInt() ?? 7,
            ),
          ),
          'index': graphic.Variable(
            accessor: (dynamic map) => (map as Map)['index'] as num,
          ),
        },
        marks: [
          graphic.PointMark(
            position: graphic.Varset('x') * graphic.Varset('y'),
            size: graphic.SizeEncode(value: pointSize),
            color: graphic.ColorEncode(
              variable: 'index',
              values: colors,
            ),
            shape: graphic.ShapeEncode(value: graphic.CircleShape()),
          ),
        ],
        axes: [
          graphic.Defaults.horizontalAxis,
          graphic.Defaults.verticalAxis,
        ],
        coord: graphic.RectCoord(),
      );
    }
  }

  /// Deserialize from JSON string
  static Widget? deserializeFromString(String jsonString) {
    try {
      final json = jsonDecode(jsonString);
      return deserialize(json);
    } catch (e) {
      return Container(
        child: Text('Deserialization error: $e'),
      );
    }
  }

  // Parser methods for deserialization
  static EdgeInsets? _parseEdgeInsets(dynamic data) {
    if (data is Map<String, dynamic>) {
      return EdgeInsets.only(
        left: data['left']?.toDouble() ?? 0,
        top: data['top']?.toDouble() ?? 0,
        right: data['right']?.toDouble() ?? 0,
        bottom: data['bottom']?.toDouble() ?? 0,
      );
    }
    return null;
  }

  static BoxDecoration? _parseBoxDecoration(dynamic data) {
    if (data is Map<String, dynamic>) {
      return BoxDecoration(
        color: data['color'] != null ? Color(data['color']) : null,
        borderRadius: data['borderRadius'] != null ? _parseBorderRadius(data['borderRadius']) : null,
        border: data['border'] != null ? _parseBorder(data['border']) : null,
        boxShadow: data['boxShadow'] != null ? _parseBoxShadows(data['boxShadow']) : null,
      );
    }
    return null;
  }

  static BorderRadius? _parseBorderRadius(dynamic data) {
    // For simplicity, return a default border radius
    // In a real implementation, you'd parse the actual BorderRadius data
    return BorderRadius.circular(8.0);
  }

  static Border? _parseBorder(dynamic data) {
    // For simplicity, return a default border
    // In a real implementation, you'd parse the actual Border data
    return Border.all(color: Colors.grey.shade300, width: 1.0);
  }

  static List<BoxShadow>? _parseBoxShadows(dynamic data) {
    if (data is List) {
      return data.map((shadow) {
        if (shadow is Map<String, dynamic>) {
          return BoxShadow(
            color: shadow['color'] != null ? Color(shadow['color']) : Colors.black,
            offset: shadow['offset'] != null
                ? Offset(shadow['offset']['dx']?.toDouble() ?? 0, shadow['offset']['dy']?.toDouble() ?? 0)
                : Offset.zero,
            blurRadius: shadow['blurRadius']?.toDouble() ?? 0,
            spreadRadius: shadow['spreadRadius']?.toDouble() ?? 0,
          );
        }
        return const BoxShadow();
      }).toList();
    }
    return null;
  }

  static MainAxisAlignment _parseMainAxisAlignment(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return MainAxisAlignment.start;
        case 1: return MainAxisAlignment.end;
        case 2: return MainAxisAlignment.center;
        case 3: return MainAxisAlignment.spaceBetween;
        case 4: return MainAxisAlignment.spaceAround;
        case 5: return MainAxisAlignment.spaceEvenly;
        default: return MainAxisAlignment.start;
      }
    }
    return MainAxisAlignment.start;
  }

  static CrossAxisAlignment _parseCrossAxisAlignment(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return CrossAxisAlignment.start;
        case 1: return CrossAxisAlignment.end;
        case 2: return CrossAxisAlignment.center;
        case 3: return CrossAxisAlignment.stretch;
        case 4: return CrossAxisAlignment.baseline;
        default: return CrossAxisAlignment.center;
      }
    }
    return CrossAxisAlignment.center;
  }

  static MainAxisSize _parseMainAxisSize(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return MainAxisSize.min;
        case 1: return MainAxisSize.max;
        default: return MainAxisSize.max;
      }
    }
    return MainAxisSize.max;
  }

  static TextAlign? _parseTextAlign(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return TextAlign.left;
        case 1: return TextAlign.right;
        case 2: return TextAlign.center;
        case 3: return TextAlign.justify;
        case 4: return TextAlign.start;
        case 5: return TextAlign.end;
        default: return null;
      }
    }
    return null;
  }

  static TextOverflow? _parseTextOverflow(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return TextOverflow.clip;
        case 1: return TextOverflow.fade;
        case 2: return TextOverflow.ellipsis;
        case 3: return TextOverflow.visible;
        default: return null;
      }
    }
    return null;
  }

  static TextStyle? _parseTextStyle(dynamic data) {
    if (data is Map<String, dynamic>) {
      return TextStyle(
        fontSize: data['fontSize']?.toDouble(),
        color: data['color'] != null ? Color(data['color']) : null,
        fontWeight: _parseFontWeight(data['fontWeight']),
        fontFamily: data['fontFamily'],
      );
    }
    return null;
  }

  static FontWeight? _parseFontWeight(dynamic value) {
    if (value is int) {
      switch (value) {
        case 0: return FontWeight.w100;
        case 1: return FontWeight.w200;
        case 2: return FontWeight.w300;
        case 3: return FontWeight.w400;
        case 4: return FontWeight.w500;
        case 5: return FontWeight.w600;
        case 6: return FontWeight.w700;
        case 7: return FontWeight.w800;
        case 8: return FontWeight.w900;
        default: return null;
      }
    }
    return null;
  }

  static Icon _parseIcon(dynamic value) {
    if (value is Map<String, dynamic>) {
      final iconData = value['iconData'];
      if (iconData != null) {
        // Map common icon code points to their respective icons
        switch (iconData) {
          case 58820: // Icons.chevron_left
            return const Icon(Icons.chevron_left);
          case 58821: // Icons.chevron_right
            return const Icon(Icons.chevron_right);
          case 57415: // Icons.calendar_today
            return const Icon(Icons.calendar_today);
          default:
            // Try to create icon from code point
            return Icon(IconData(iconData, fontFamily: 'MaterialIcons'));
        }
      }
    }

    // Fallback for string-based icon detection
    if (value != null && value.toString().contains('chevron_left')) {
      return const Icon(Icons.chevron_left);
    } else if (value != null && value.toString().contains('chevron_right')) {
      return const Icon(Icons.chevron_right);
    }

    return const Icon(Icons.help_outline);
  }

static ShapeBorder? _parseShapeBorder(dynamic data) {
  if (data is Map<String, dynamic>) {
    final type = data['type'];

    if (type == 'RoundedRectangleBorder') {
      // Parse borderRadius: supports "BorderRadius.circular(x)" string or number
      double radius = 0.0;

      final dynamic radiusData = data['borderRadius'];
      if (radiusData is String && radiusData.contains('BorderRadius.circular')) {
        final match = RegExp(r'(\d+(\.\d+)?)').firstMatch(radiusData);
        if (match != null) {
          radius = double.tryParse(match.group(0) ?? '0') ?? 0.0;
        }
      } else if (radiusData is num) {
        radius = radiusData.toDouble();
      }

      // Parse side if available
      BorderSide side = BorderSide.none;
      if (data['side'] is Map<String, dynamic>) {
        final sideData = data['side'];
        final int colorValue = sideData['color'] ?? Colors.grey.value;
        final double width = (sideData['width'] ?? 1.0).toDouble();

        side = BorderSide(color: Color(colorValue), width: width);
      }

      return RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radius),
        side: side,
      );
    }
  }

  return null;
}

  static DateRangeFormat _parseDateRangeFormat(dynamic value) {
    if (value == null) return DateRangeFormat.standard;

    switch (value.toString().toLowerCase()) {
      case 'short':
        return DateRangeFormat.short;
      case 'long':
        return DateRangeFormat.long;
      case 'custom':
        return DateRangeFormat.custom;
      case 'iso':
        return DateRangeFormat.iso;
      case 'standard':
      default:
        return DateRangeFormat.standard;
    }
  }

  // Helper methods to extract radius from series data
  static int _getRadiusFromSeries(Map<String, dynamic> json) {
    final seriesList = json['series'] as List?;
    if (seriesList != null && seriesList.isNotEmpty) {
      final firstSeries = seriesList.first as Map<String, dynamic>;
      final radius = firstSeries['radius'] as String?;
      if (radius != null) {
        // Extract percentage value from string like "85%"
        final match = RegExp(r'(\d+)%').firstMatch(radius);
        if (match != null) {
          return int.tryParse(match.group(1) ?? '85') ?? 85;
        }
      }
    }
    return 85; // Default
  }

  static int _getInnerRadiusFromSeries(Map<String, dynamic> json) {
    final seriesList = json['series'] as List?;
    if (seriesList != null && seriesList.isNotEmpty) {
      final firstSeries = seriesList.first as Map<String, dynamic>;
      final innerRadius = firstSeries['innerRadius'] as String?;
      if (innerRadius != null) {
        // Extract percentage value from string like "55%"
        final match = RegExp(r'(\d+)%').firstMatch(innerRadius);
        if (match != null) {
          return int.tryParse(match.group(1) ?? '55') ?? 55;
        }
      }
    }
    return 55; // Default
  }

  /// Get serialization statistics
  static Map<String, dynamic> getSerializationStats(Widget widget) {
    final json = serialize(widget);
    
    int supportedWidgets = 0;
    int unsupportedWidgets = 0;
    Set<String> widgetTypes = {};
    
    void countWidgets(Map<String, dynamic> data) {
      if (data.containsKey('type')) {
        widgetTypes.add(data['type']);
        if (data.containsKey('unsupported') && data['unsupported'] == true) {
          unsupportedWidgets++;
        } else {
          supportedWidgets++;
        }
      }
      
      // Recursively count children
      if (data.containsKey('children') && data['children'] is List) {
        for (var child in data['children']) {
          if (child is Map<String, dynamic>) {
            countWidgets(child);
          }
        }
      }
      
      if (data.containsKey('child') && data['child'] is Map<String, dynamic>) {
        countWidgets(data['child']);
      }
    }
    
    countWidgets(json);
    
    return {
      'totalWidgets': supportedWidgets + unsupportedWidgets,
      'supportedWidgets': supportedWidgets,
      'unsupportedWidgets': unsupportedWidgets,
      'supportRate': supportedWidgets / (supportedWidgets + unsupportedWidgets) * 100,
      'widgetTypes': widgetTypes.toList(),
      'jsonSize': jsonEncode(json).length,
    };
  }

}
class _ChartData {
  final String category;
  final double value;

  _ChartData(this.category, this.value);
}

class _BubbleChartData {
  final double x;
  final double y;
  final double size;

  _BubbleChartData(this.x, this.y, this.size);
}

class _ScatterChartData {
  final double x;
  final double y;

  _ScatterChartData(this.x, this.y);
}
